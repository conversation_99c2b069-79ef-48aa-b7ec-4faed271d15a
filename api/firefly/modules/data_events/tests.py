from unittest import mock

from django.contrib.auth.models import Group

from firefly.core.alias.models import AliasMapping, AliasName
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.constants import DESIGNER_USER_GROUP
from firefly.modules.data_events.constants import IN_APP_FEEDBACK


class InAppFeedbackTestCase(FireflyTestCase):
    def test_in_app_feedback_requests(self):
        event = "Patient in-app feedback"
        feedback = "Test feedback response"
        payload = {
            "event": event,
            "properties": {
                "feedback": feedback,
            },
        }
        response = self.client.post("/data-events/", payload, format="json")
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.json()["user"], self.patient.pk)
        self.assertEqual(response.json()["event"], event)
        self.assertEqual(response.json()["properties"]["feedback"], feedback)

    @mock.patch("firefly.modules.data_events.postsave.send_slack_message")
    def test_data_event_slack(self, mock_send_slack_message):
        payload = {
            "event": IN_APP_FEEDBACK,
            "properties": {
                "feedback": "I don't like sand. It's coarse and rough and irritating and it gets everywhere."
            },
        }

        designer_group, created = Group.objects.get_or_create(name=DESIGNER_USER_GROUP)
        self.provider.groups.add(designer_group)
        AliasMapping.objects.create(content_object=self.provider, alias_name=AliasName.SLACK, alias_id="ANAK1N")

        response = self.client.post("/data-events/", payload, format="json")
        self.assertEqual(response.status_code, 201)

        # does not get called because there are no designer users
        mock_send_slack_message.send.assert_called_once()

    @mock.patch("firefly.modules.data_events.postsave.is_person_in_firefly")
    @mock.patch("firefly.modules.data_events.postsave.send_slack_message")
    def test_slack_alert_only_to_firefly(self, mock_send_slack_message, mock_is_person_in_firefly):
        payload = {
            "event": IN_APP_FEEDBACK,
            "properties": {
                "feedback": "I don't like sand. It's coarse and rough and irritating and it gets everywhere."
            },
        }

        mock_is_person_in_firefly.return_value = True
        response = self.client.post("/data-events/", payload, format="json")
        self.assertEqual(response.status_code, 201)
        mock_send_slack_message.send.assert_called_once()
        mock_send_slack_message.send.reset_mock()
        mock_is_person_in_firefly.reset_mock()

        # Case 2: When Data even sender belongs to non-FF tenant
        mock_is_person_in_firefly.return_value = False
        response = self.client.post("/data-events/", payload, format="json")
        self.assertEqual(response.status_code, 201)
        mock_send_slack_message.send.assert_not_called()
        mock_is_person_in_firefly.reset_mock()
