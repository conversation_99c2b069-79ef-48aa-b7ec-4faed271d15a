from django.conf import settings

from firefly.core.services.slack.constants import Slack<PERSON>hannel
from firefly.core.services.slack.tasks import send_slack_message
from firefly.core.services.slack.utils import get_slack_ids
from firefly.core.user.constants import DESIGNER_USER_GROUP
from firefly.core.user.utils import get_ids_in_group
from firefly.modules.data_events.constants import IN_APP_FEEDBACK
from firefly.modules.tenants.utils import is_person_in_firefly

FEEDBACK_TEMPLATE = f"""
<{settings.ADMIN_BASE_URL}/admin/data_events/inappfeedback/{{feedback_id}}/change/|NEW PATIENT FEEDBACK>
"""


def notify_slack(in_app_feedback):
    person = in_app_feedback.user.person
    if in_app_feedback.event == IN_APP_FEEDBACK and is_person_in_firefly(person):
        designer_ids = get_ids_in_group(DESIGNER_USER_GROUP)
        slack_ids = get_slack_ids(designer_ids) or []
        send_slack_message.send(
            markdown_text=FEEDBACK_TEMPLATE.format(feedback_id=in_app_feedback.id),
            slack_ids=slack_ids,
            slack_channel=SlackChannel.PATIENT_FEEDBACK_ALERTS,
        )


# This is a very important constant
# treat it like INSTALLED_APPS in settings.py
# data_events/models.py::run_dataevent_postsave_hooks for more context
POSTSAVE_HOOKS = [notify_slack]
