import itertools

from django.contrib import admin

from firefly.core.utils.admin import link_field
from firefly.modules.data_events.models import InAppFeedback
from firefly.modules.firefly_django.admin import BaseModelV3AdminMixin


@admin.register(InAppFeedback)
class InAppFeedbackAdmin(BaseModelV3AdminMixin):
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [
                "event",
                link_field("user"),
                "created_at",
            ],
        )
    )
