from rest_framework.generics import CreateAPIView
from rest_framework.permissions import IsAuthenticated

from firefly.core.user.permissions import IsOwnerV2
from firefly.modules.data_events.models import InAppFeedback
from firefly.modules.data_events.serializers import InAppFeedbackSerializer


class InAppFeedbackCreateAPIView(CreateAPIView):
    model = InAppFeedback
    serializer_class = InAppFeedbackSerializer
    permission_classes = (IsAuthenticated, IsOwnerV2)
    skip_tenant_access_check = True

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)
