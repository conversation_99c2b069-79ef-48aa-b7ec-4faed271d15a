# Generated by Django 4.2.20 on 2025-05-27 13:35

import pgtrigger.compiler
import pgtrigger.migrations
from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ("programs", "0006_remove_programenrollment_insert_insert_and_more"),
    ]

    operations = [
        pgtrigger.migrations.RemoveTrigger(
            model_name="program",
            name="insert_insert",
        ),
        pgtrigger.migrations.RemoveTrigger(
            model_name="program",
            name="update_update",
        ),
        migrations.RemoveField(
            model_name="program",
            name="hint_id",
        ),
        migrations.RemoveField(
            model_name="programevent",
            name="hint_id",
        ),
        pgtrigger.migrations.AddTrigger(
            model_name="program",
            trigger=pgtrigger.compiler.Trigger(
                name="insert_insert",
                sql=pgtrigger.compiler.UpsertTriggerSql(
                    func='INSERT INTO "programs_programevent" ("created_at", "created_by_id", "deleted", "is_clinical", "pgh_context_id", "pgh_created_at", "pgh_label", "pgh_obj_id", "uid", "updated_at", "updated_by_id") VALUES (NEW."created_at", NEW."created_by_id", NEW."deleted", NEW."is_clinical", _pgh_attach_context(), NOW(), \'insert\', NEW."uid", NEW."uid", NEW."updated_at", NEW."updated_by_id"); RETURN NULL;',
                    hash="f9188590a7b5f60d4d1794bc3400e8c716997c2f",
                    operation="INSERT",
                    pgid="pgtrigger_insert_insert_5a554",
                    table="programs_program",
                    when="AFTER",
                ),
            ),
        ),
        pgtrigger.migrations.AddTrigger(
            model_name="program",
            trigger=pgtrigger.compiler.Trigger(
                name="update_update",
                sql=pgtrigger.compiler.UpsertTriggerSql(
                    condition="WHEN (OLD.* IS DISTINCT FROM NEW.*)",
                    func='INSERT INTO "programs_programevent" ("created_at", "created_by_id", "deleted", "is_clinical", "pgh_context_id", "pgh_created_at", "pgh_label", "pgh_obj_id", "uid", "updated_at", "updated_by_id") VALUES (NEW."created_at", NEW."created_by_id", NEW."deleted", NEW."is_clinical", _pgh_attach_context(), NOW(), \'update\', NEW."uid", NEW."uid", NEW."updated_at", NEW."updated_by_id"); RETURN NULL;',
                    hash="1627cc346451e95c48fcf5b4b558b8655893a2e0",
                    operation="UPDATE",
                    pgid="pgtrigger_update_update_7f86c",
                    table="programs_program",
                    when="AFTER",
                ),
            ),
        ),
    ]
