import logging
import os
from typing import List

import waffle
from django.contrib.gis.db import models as gis_models
from django.contrib.gis.geos import Point
from django.db import models
from django.db.models import JSONField, Q, constraints
from django.db.models.constraints import UniqueConstraint
from django_deprecate_fields import deprecate_field

from firefly.modules.facts.models import SpecialtyGroup
from firefly.modules.firefly_django.models import BaseModelV3
from firefly.modules.health_plan.models import HealthPlanConfiguration
from firefly.modules.insurance.models import Contract, InsurancePayer
from firefly.modules.states.models import State

logger = logging.getLogger(__name__)


def get_logos_folder(instance, filename):
    return os.path.join("contracts", str(instance.pk), "logos", filename)


class CuratedProviderCareOrgNameChoice(models.TextChoices):
    UNKNOWN = "UNKNOWN", "UNKNOWN"


class AgreementTypeConfig:
    # Partners where services are billed directly to Firefly Medical Group and
    #  are considered as an extension of Firefly services
    DIRECT_CONTRACT = "direct_contract"
    # Partners where the payment is made by the insurance payer but Firefly prioritizes them above others
    #  due to low cost/better access/better quality/agreement
    PREFERRED_VENDOR = "preferred_vendor"
    # Partners where clients have setup direct contracts and are paid directly by the client
    CLIENT_CONTRACT = "client_contract"


AGREEMENT_TYPE_CHOICES = [
    (AgreementTypeConfig.DIRECT_CONTRACT, "Direct Contract (Premium Partner)"),
    (AgreementTypeConfig.PREFERRED_VENDOR, "Preferred Vendor (Partner)"),
    (AgreementTypeConfig.CLIENT_CONTRACT, "Client Partner"),
]


class PartnershipTypeConfig:
    # All providers in the facility are marked as partners
    CARE_ORG = "care_org"
    # Provider is marked as a partner at a specific facility location
    PROVIDER_CARE_ORG = "provider_care_org"


PARTNERSHIP_TYPE_CHOICES = [
    (PartnershipTypeConfig.CARE_ORG, "Care Organization"),
    (PartnershipTypeConfig.PROVIDER_CARE_ORG, "Provider(at Care Organization)"),
]


class CuratedProviderRankingReasonConfig:
    HIGH_COST = "High Cost"
    LOW_COST = "Low Cost"


CURATED_PROVIDER_RANKING_REASON_CHOICES = [
    (CuratedProviderRankingReasonConfig.HIGH_COST, "High Cost"),
    (CuratedProviderRankingReasonConfig.LOW_COST, "Low Cost"),
]


class CuratedProviderRankingLevelConfig:
    # All providers in the facility are downranked or upranked
    CARE_ORG = "care_org"
    # Provider is downranked or upranked at a specific facility location
    PROVIDER_CARE_ORG = "provider_care_org"


CURATED_PROVIDER_RANKING_LEVEL_CHOICES = [
    (CuratedProviderRankingLevelConfig.CARE_ORG, "Care Organization"),
    (CuratedProviderRankingLevelConfig.PROVIDER_CARE_ORG, "Provider(at Care Organization)"),
]


class CuratedProviderType(models.TextChoices):
    IN_PERSON = "In Person", "In Person"
    VIRTUAL = "Virtual", "Virtual"
    IN_HOME = "In Home", "In Home"
    AT_HOME_TEST_KIT = "At Home Test Kit", "At Home Test Kit"


class PartnershipType(models.TextChoices):
    URGENT_RETAIL_CARE = "Urgent/Retail Care", "Urgent/Retail Care"
    IN_HOME_SERVICES = "In Home Services", "In Home Services"
    TEST_KIT = "Test Kit", "Test Kit"
    LAB = "Lab", "Lab"
    SPECIALIST = "Specialist", "Specialist"
    IMAGING_CENTER = "Imaging Center", "Imaging Center"
    DME = "DME", "DME"


class PointSolutionType(models.TextChoices):
    FIREFLY = "Firefly", "Firefly"
    EMPLOYER = "Employer", "Employer"


class CuratedProvider(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    npi = models.CharField(  # noqa: TID251
        max_length=10,
        null=True,
        blank=True,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    care_org_name = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    address_line_1 = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    address_line_2 = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    city = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    zip_code = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    state = models.ForeignKey(
        State,
        null=True,
        on_delete=models.SET_NULL,
    )
    # This is where we store care_org_name without space, special characters
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    sanitized_care_org_name = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    provider_type = models.CharField(  # noqa: TID251
        max_length=100,
        blank=True,
        null=True,
        choices=CuratedProviderType.choices,
    )
    # Stores the information if the provider is in-person or some other type from the choices
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    first_name = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    last_name = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
    )

    # Latitude and Longitude ##.###### and ###.######
    latitude = models.DecimalField(max_digits=8, decimal_places=6, null=True, blank=True, default=None)
    longitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True, default=None)

    # Represents a point on the earth by using a combination of longitude and latitude
    point = gis_models.PointField(blank=True, null=True, spatial_index=True, srid=4326, geography=True)

    class Meta(BaseModelV3.Meta):
        constraints = [
            # This is used for curating contact information and availability data at care_org level.
            constraints.UniqueConstraint(
                fields=["care_org_name", "zip_code"],
                condition=Q(deleted=None, npi=None, care_org_name__isnull=False, zip_code__isnull=False),
                name="network_curatedprovider_unique_care_org_zipcode",
            ),
            # We have some care_org level partnership for Umass and MH
            constraints.UniqueConstraint(
                fields=["care_org_name"],
                condition=Q(deleted=None, zip_code=None, npi=None, care_org_name__isnull=False),
                name="network_curatedprovider_unique_care_org",
            ),
            # This used for tagging partners at the provider level and curating provider information at provider level.
            constraints.UniqueConstraint(
                fields=["npi", "zip_code", "care_org_name"],
                condition=Q(deleted=None, care_org_name__isnull=False, npi__isnull=False, zip_code__isnull=False),
                name="network_curatedprovider_unique_npi_care_org_zipcode",
            ),
        ]

    def save(self, *args, **kwargs):
        from firefly.modules.referral.utils.provider_search_utils import get_sanitized_name

        # If the care_org name is None then store it as "UNKNOWN"
        # This would help to avoid storing duplicate curated providers and uniques will work as expected
        if not self.care_org_name:
            self.care_org_name = CuratedProviderCareOrgNameChoice.UNKNOWN

        # Update sanitized_care_org_name
        self.sanitized_care_org_name = get_sanitized_name(self.care_org_name)

        # Update the location point
        if self.latitude and self.longitude and not self.point:
            self.point = Point(
                x=float(self.longitude),
                y=float(self.latitude),
                srid=4326,
            )
        super(CuratedProvider, self).save(*args, **kwargs)


# This is useful to Up-rank or Down-rank curated providers in the provider search results
class Ranking(BaseModelV3):
    curated_provider = models.ForeignKey(
        CuratedProvider,
        related_name="rankings",
        null=False,
        on_delete=models.CASCADE,
    )
    # This defines the level at which ranking should be applied
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ranking_level = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
        choices=CURATED_PROVIDER_RANKING_LEVEL_CHOICES,
    )
    # This stores the reason why we are up-ranking or down-ranking
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    reason = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
        choices=CURATED_PROVIDER_RANKING_REASON_CHOICES,
    )
    # Positive for up-rank and negative for down-rank
    rank_change = models.IntegerField(
        null=True,
        blank=True,
    )


class Partnership(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    partner_name = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
    )

    # The type of agreement dictates not only the terms of the agreement,
    # but how we'll end up representing it downstream visually.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    agreement_type = models.CharField(  # noqa: TID251
        max_length=255,
        blank=False,
        null=False,
        choices=AGREEMENT_TYPE_CHOICES,
    )
    # Boolean fields to denote whether the agreement covers care, coverage or care_and_coverage members.
    can_accept_care_members = models.BooleanField(null=False, blank=False, verbose_name="Serves Care Only members")
    can_accept_coverage_members = models.BooleanField(
        null=False, blank=False, verbose_name="Serves Coverage Only members"
    )
    can_accept_care_and_coverage_members = models.BooleanField(
        null=False, blank=False, verbose_name="Serves Care + Coverage members"
    )
    # This parameter is useful when a partnership is valid for all the zipcode so that we won't need to create
    # Curated Providers for every zipcode
    is_valid_for_all_zipcodes = models.BooleanField(
        null=True,
        blank=True,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    partnership_type = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
        choices=PartnershipType.choices,
    )
    # This parameter is useful to enable or disable partnerships
    # When disabled, this partnership or partnership providers will not show in suggestions
    is_active = models.BooleanField(
        null=True,
        blank=True,
    )
    # This is to designate if we should automatically assign a feedback form when members have visits
    # with these partners
    requires_member_feedback = models.BooleanField(
        null=True,
        blank=True,
    )

    # Add description about partner
    description = models.TextField(null=True, blank=True)

    # Partnership logo
    logo = models.ImageField(null=True, blank=True, upload_to=get_logos_folder)

    class Meta(BaseModelV3.Meta):
        constraints = [
            constraints.UniqueConstraint(
                fields=["partner_name"],
                condition=Q(deleted=None),
                name="network_partnership_unique_partnername",
            ),
        ]


class Availability(BaseModelV3):
    # This will be a one to many relation as one curated faclity can have Availability
    # at the department level and facility level
    curated_provider = models.ForeignKey(
        CuratedProvider,
        related_name="availabilities",
        null=False,
        on_delete=models.CASCADE,
    )
    # It stores the number of days after which Provider/Facility is next available on
    number_of_days_till_next_availability = models.IntegerField(
        null=True,
        blank=True,
    )
    # Date on which availability or does_provider_exist information is added
    added_at = models.DateTimeField(
        null=True,
        blank=True,
    )
    # To know if the availability is at department level
    specialty_group = models.ForeignKey(
        SpecialtyGroup,
        related_name="availabilities",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    # This for tracking if provider is accepting new patients
    does_provider_exist = models.BooleanField(
        null=True,
        blank=True,
    )
    # This is a derived field - it will tell us whether a provider or facility is available or not
    is_available = models.BooleanField(
        null=True,
        blank=True,
    )


class ContactInformation(BaseModelV3):
    # This will be a one to many relation as one curated faclity can have contact information
    # at the department level and facility level
    curated_provider = models.ForeignKey(
        CuratedProvider,
        related_name="contact_informations",
        null=False,
        on_delete=models.CASCADE,
    )
    # To know if the ContactInformation is at department level
    specialty_group = models.ForeignKey(
        SpecialtyGroup,
        related_name="contact_informations",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    phone = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    fax = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # To know if the contact information are verified
    is_verified = models.BooleanField(
        null=True,
        blank=True,
    )


class CuratedProviderPartnership(BaseModelV3):
    """
    Table to assist in the ManyToMany join between CuratedProvider and Partnership.
    """

    curated_provider = models.ForeignKey(
        CuratedProvider,
        related_name="curatedprovider_partnerships",
        null=False,
        on_delete=models.CASCADE,
    )

    partnership = models.ForeignKey(
        Partnership,
        related_name="curatedprovider_partnerships",
        null=False,
        on_delete=models.CASCADE,
    )

    # Stores the level at which the curated provider partnership is valid
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    partnership_level = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
        choices=PARTNERSHIP_TYPE_CHOICES,
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["curated_provider", "partnership", "partnership_level"],
                condition=Q(deleted=None),
                name="network_curatedprovider_partnership_partnershiplevel_uniq",
            )
        ]


def get_unique_fields_for_care_org_level_partnership_records() -> List[str]:
    return [
        "care_org_name",
        "address_line_1",
        "zip_code",
        "agreement_type",
    ]


def get_unique_fields_for_provider_at_care_org_level_partnership_records() -> List[str]:
    return [
        "npi",
        "care_org_name",
        "address_line_1",
        "zip_code",
        "agreement_type",
    ]


# Model stores partnership information before it is processed
# and stored in the Curated Provider models.
# For example: The raw partnership for an entire facility
# might contain individual rows for each provider in the facility.
# Examples include MH and UMass partnerships - however - before these
# are stored in the CuratedProvider models - a single row representing
# the facility is stored in the CuratedProvider model.
# Having the raw partnership data - enables validation that the compression
# and the matching logic on top of CuratedProvider work as expected
# and also enable an exhaustive export when needed
class PartnershipRecord(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    npi = models.CharField(  # noqa: TID251
        max_length=10,
        null=True,
        blank=True,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    care_org_name = models.CharField(  # noqa: TID251
        max_length=255,
        blank=False,
        null=False,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    address_line_1 = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    address_line_2 = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    city = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    zip_code = models.CharField(max_length=255, blank=False, null=False)  # noqa: TID251
    state = models.ForeignKey(
        State,
        null=True,
        on_delete=models.SET_NULL,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    partner_name = models.CharField(  # noqa: TID251
        max_length=255,
        blank=False,
        null=False,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    partnership_level = models.CharField(  # noqa: TID251
        max_length=255,
        blank=False,
        null=False,
        choices=PARTNERSHIP_TYPE_CHOICES,
    )
    # The type of agreement dictates not only the terms of the agreement,
    # but how we'll end up representing it downstream visually.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    agreement_type = models.CharField(  # noqa: TID251
        max_length=255,
        blank=False,
        null=False,
        choices=AGREEMENT_TYPE_CHOICES,
    )
    # Boolean fields to denote whether the agreement covers care, coverage or care_and_coverage members.
    can_accept_care_members = models.BooleanField(null=False, blank=False, verbose_name="Serves Care Only members")
    can_accept_coverage_members = models.BooleanField(
        null=False, blank=False, verbose_name="Serves Coverage Only members"
    )
    can_accept_care_and_coverage_members = models.BooleanField(
        null=False, blank=False, verbose_name="Serves Care + Coverage members"
    )
    # stores whether the entry is marked as a partner during provider search
    is_tagged_as_partner = models.BooleanField(null=True, blank=True, default=False)
    # stores whether a row has been processed by the verification script
    # to mark whether the entry gets tagged as a partner
    is_processed = models.BooleanField(null=True, blank=True, default=False)
    # stores the unprocessed output from the search endpoints
    # so that results can be reprocessed without having to re-fetch
    raw_results = JSONField(null=True, blank=True)
    # stores the output from the provider search endpoints
    results = JSONField(null=True, blank=True)
    # Maps a raw partnership record to the corresponding curated provider entry
    curated_provider = models.ForeignKey(
        CuratedProvider,
        related_name="partnership_records",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    # Tie back to the import job that ingested this partnership record
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    import_job_name = models.CharField(  # noqa: TID251
        max_length=255,
        blank=False,
        null=False,
    )
    # Stores the information if the provider is in-person or some other type from the choices
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    provider_type = models.CharField(  # noqa: TID251
        max_length=100,
        blank=True,
        null=True,
        choices=CuratedProviderType.choices,
    )

    # Stores the Provider name information if present
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    first_name = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    last_name = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
    )

    class Meta(BaseModelV3.Meta):
        constraints = [
            constraints.UniqueConstraint(
                fields=get_unique_fields_for_care_org_level_partnership_records(),
                condition=Q((Q(npi__exact="") | Q(npi__isnull=True)), deleted=None),
                name="network_raw_partnership_unique_care_org_partnership",
            ),
            constraints.UniqueConstraint(
                fields=get_unique_fields_for_provider_at_care_org_level_partnership_records(),
                condition=Q(~Q(npi__exact=""), Q(npi__isnull=False), deleted=None),
                name="network_raw_partnership_unique_provider_at_care_org_partnership",
            ),
        ]

    def delete(self, *args, **kwargs):
        curated_provider_id = None
        partnership_record_id = self.id
        if self.curated_provider and self.curated_provider.id:
            curated_provider_id = self.curated_provider.id

        if curated_provider_id:
            try:
                # Delete related partnerships and curated provider mappings
                CuratedProviderPartnership.objects.filter(curated_provider=curated_provider_id).delete()
            except Exception:
                logger.exception("Unable to delete Partnership for partnership record %d", partnership_record_id)

        # Call the parent class's delete method to delete the PartnershipRecord instance
        return super().delete(*args, **kwargs)


# DEPRECATED: This model is deprecated and should not be used.
class PartnerEntity(BaseModelV3):
    # stores the unprocessed output from the ribbon endpoints
    # so that results can be reprocessed without having to re-fetch
    raw_ribbon_results = deprecate_field(JSONField(null=True, blank=True))
    # stores the output from the provider search endpoints
    results = deprecate_field(JSONField(null=True, blank=True))
    # stores whether a row has an exact match in the Ribbon systems
    has_exact_match_in_ribbon = deprecate_field(models.BooleanField(null=False, blank=False, default=False))
    # Tie back to the import job that ingested this record
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    import_job_name = deprecate_field(models.CharField(max_length=255, blank=False, null=False))  # noqa: TID251
    # UUID of the entry in Ribbon
    # Ideally stored in AliasMapping - but is also stored on this model - since the model
    # is dedicated to map our PartnerEntry with that of an external system
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ribbon_uuid = deprecate_field(models.CharField(max_length=255, blank=True, null=True))  # noqa: TID251
    # stores whether a row has been processed by the matching script
    is_processed = deprecate_field(models.BooleanField(null=True, blank=True))

    class Meta(BaseModelV3.Meta):
        abstract = True


# DEPRECATED: This model is deprecated and should not be used.
class PartnerLocation(PartnerEntity):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    care_org_name = deprecate_field(models.CharField(max_length=255, blank=False, null=False))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    address_line_1 = deprecate_field(models.CharField(max_length=255, blank=False, null=False))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    zip_code = deprecate_field(models.CharField(max_length=5, blank=False, null=False))  # noqa: TID251
    has_more_than_one_match_in_ribbon = deprecate_field(models.BooleanField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    partner_name = deprecate_field(models.CharField(max_length=255, blank=True, null=True))  # noqa: TID251


# DEPRECATED: This model is deprecated and should not be used.
class PartnerProvider(PartnerEntity):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    npi = deprecate_field(models.CharField(max_length=10, null=False, blank=False))  # noqa: TID251


# DEPRECATED: This model is deprecated and should not be used.
class PartnerProviderLocation(PartnerEntity):
    provider = deprecate_field(
        models.ForeignKey(
            PartnerProvider,
            related_name="provider_locations",
            null=False,
            blank=False,
            on_delete=models.CASCADE,
        )
    )
    location = deprecate_field(
        models.ForeignKey(
            PartnerLocation,
            related_name="provider_locations",
            null=False,
            blank=False,
            on_delete=models.CASCADE,
        )
    )
    # stores whether the entry is marked as a partner during provider search
    is_tagged_as_partner = deprecate_field(models.BooleanField(null=True, blank=True))

    class Meta(BaseModelV3.Meta):
        constraints = [
            UniqueConstraint(
                fields=["provider", "location"],
                condition=Q(deleted=None),
                name="unique_provider_location_mapping",
            ),
        ]


class ActionTypeConfig:
    ADDED = "added"
    REMOVED = "removed"


ACTION_TYPE_CHOICES = [
    (ActionTypeConfig.ADDED, "Added"),
    (ActionTypeConfig.REMOVED, "Removed"),
]


# If the system finds that the provider exists in Ribbon
# and the location was perfectly matched in the Ribbon system
# however - the location does not appear in the list of locations
# for the provider - the location is added to the list of locations
# for the provider.
# This model tracks any such additions that were made so that
# these can be reversed if needed.
# DEPRECATED: This model is deprecated and should not be used.
class PartnerProviderLocationRibbonMapping(BaseModelV3):
    provider_location = deprecate_field(
        models.OneToOneField(
            PartnerProviderLocation,
            related_name="ribbon_mapping",
            null=False,
            blank=False,
            on_delete=models.CASCADE,
        )
    )
    # should be same as provider_location.location.ribbon_uuid
    # however is being explicitly stored here in case the uuid changes
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    location_uuid = deprecate_field(models.CharField(max_length=255, blank=False, null=False))  # noqa: TID251
    # whether the association was added/ removed in ribbon
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    action = deprecate_field(
        models.CharField(  # noqa: TID251
            max_length=255,
            blank=False,
            null=False,
            choices=ACTION_TYPE_CHOICES,
        )
    )


# The insurance mapping is required for Partnership which have agreement type as Preferred Vendor
class PartnershipInsurancePayer(BaseModelV3):
    partnership = models.ForeignKey(
        Partnership,
        related_name="partnershipinsurancepayer_partnerships",
        null=False,
        on_delete=models.CASCADE,
    )
    insurance_payer = models.ForeignKey(
        InsurancePayer,
        related_name="partnershipinsurancepayer_insurance_payers",
        null=False,
        on_delete=models.CASCADE,
    )
    PLAN_TYPE_HMO = "HMO"
    PLAN_TYPE_PPO = "PPO"
    PLAN_TYPE_ANY = "ANY"
    PLAN_TYPE_CHOICES = [
        (PLAN_TYPE_HMO, "HMO"),
        (PLAN_TYPE_PPO, "PPO"),
        (PLAN_TYPE_ANY, "ANY"),
    ]
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    insurance_plan_type = models.CharField(max_length=3, choices=PLAN_TYPE_CHOICES, blank=True, null=True)  # noqa: TID251
    state = models.ForeignKey(
        State,
        null=True,
        on_delete=models.SET_NULL,
    )
    # Tie back to the import job that ingested this mapping
    # Useful for deleting the older mappings
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    import_job_name = models.CharField(  # noqa: TID251
        max_length=255,
        blank=False,
        null=False,
    )
    # We have different contracting based on group-id for Firefly Insurance.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    group_number = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251


# Stores the Partnerships and there contract mappings
class PartnershipContract(BaseModelV3):
    partnership = models.ForeignKey(
        Partnership,
        related_name="partnershipcontract_partnerships",
        null=False,
        on_delete=models.CASCADE,
    )
    contract = models.ForeignKey(
        Contract,
        related_name="partnershipcontract_contracts",
        null=False,
        on_delete=models.CASCADE,
    )
    # Stores if the contract is valid for primary subscriber only or it is also valid for dependant
    is_primary_subscriber_only = models.BooleanField(null=True, blank=True)
    # Ties back to the import job that ingested this mapping
    # Useful for deleting the older mappings
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    import_job_name = models.CharField(  # noqa: TID251
        max_length=255,
        blank=False,
        null=False,
    )


# This model stores states which are excluded for partnerships
class PartnershipServicingStateExclusion(BaseModelV3):
    partnership = models.ForeignKey(
        Partnership,
        related_name="stateexclusion_partnerships",
        on_delete=models.CASCADE,
    )
    state = models.ForeignKey(
        State,
        on_delete=models.CASCADE,
    )


# This model stores the FFNB coverage for the prospect member zip codes
class FireflyNearbyCoverage(BaseModelV3):
    # Combination of date + target name
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    import_job_name = models.CharField(  # noqa: TID251
        max_length=255,
        blank=False,
        null=False,
    )
    # Prospect name
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    target_name = models.CharField(  # noqa: TID251
        max_length=255,
        blank=False,
        null=False,
    )
    # Prospect zip code
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    zip_code = models.CharField(max_length=255, blank=False, null=False)  # noqa: TID251
    # Count of members in the prospect zip code
    member_count = models.IntegerField(
        null=True,
        blank=True,
    )
    # Stores whether a row has been already processed for the coverage analysis
    is_processed = models.BooleanField(null=True, blank=True, default=False)
    coverage_range_in_miles = models.IntegerField(
        null=True,
        blank=True,
    )
    # Booleans to record the coverage details
    has_urgent_care_within_range = models.BooleanField(
        null=True,
        blank=True,
    )
    has_in_home_services_within_range = models.BooleanField(
        null=True,
        blank=True,
    )
    has_lab_within_range = models.BooleanField(
        null=True,
        blank=True,
    )
    has_ffnb_within_range = models.BooleanField(
        null=True,
        blank=True,
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["zip_code", "target_name", "import_job_name"],
                condition=Q(deleted=None),
                name="unique_zip_code_target_name_import_job_name_if_not_deleted",
            )
        ]


class HealthPlanConfigurationPartnership(BaseModelV3):
    """
    Table to assist in the ManyToMany join between HealthPlanConfiguration and Partnership.
    """

    health_plan_configuration = models.ForeignKey(
        HealthPlanConfiguration,
        related_name="healthplanconfiguration_partnerships",
        null=False,
        on_delete=models.CASCADE,
    )

    partnership = models.ForeignKey(
        Partnership,
        related_name="healthplanconfiguration_partnerships",
        null=False,
        on_delete=models.CASCADE,
    )

    # Stores the URL for the contract partnership
    point_solution_url = models.URLField(null=True, blank=True)

    # Stores the type of partnership
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    point_solution_type = models.CharField(  # noqa: TID251
        max_length=100,
        null=False,
        choices=PointSolutionType.choices,
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["health_plan_configuration", "partnership", "point_solution_type"],
                condition=Q(deleted=None),
                name="network_healthplanconfiguration_partnerships_pointsolutiontype_uniq",
            )
        ]


# This stores TIN Information for each location
class TaxIdentifier(BaseModelV3):
    tin = models.TextField(unique=True)

    name = models.TextField(blank=True, null=True)

    legal_name = models.TextField(blank=True, null=True)

    address = models.TextField(blank=True, null=True)

    confirmed = models.BooleanField(blank=True, null=True)

    class Meta(BaseModelV3.Meta):
        abstract = False

    def save(self, *args, **kwargs):
        from firefly.modules.network.tasks import update_tin_details_for_tax_identifier
        from firefly.modules.referral.constants import WAFFLE_SWITCH_STORE_TINS

        created = self.pk is None
        super(TaxIdentifier, self).save(*args, **kwargs)

        if waffle.switch_is_active(WAFFLE_SWITCH_STORE_TINS) and created:
            # Grab TIN details from Ribbon and update this object
            update_tin_details_for_tax_identifier.send(self.tin)


# It stores the Provider details and there recommendation status calculated based on the Ribbon data
class ProviderRecommendationAnalysis(BaseModelV3):
    class ProviderRecommendationAnalysisVendor(models.TextChoices):
        RIBBON = "Ribbon", "Ribbon"

    class RecommendationStatusChoices(models.TextChoices):
        NOT_RECOMMENDED = "NOT_RECOMMENDED", "NOT_RECOMMENDED"
        RECOMMENDED = "RECOMMENDED", "RECOMMENDED"
        NEUTRAL = "NEUTRAL", "NEUTRAL"

    npi = models.TextField(
        max_length=10,
        null=False,
        blank=False,
    )
    zip_code = models.TextField(max_length=255, blank=True, null=True)

    # Store whether or not Firefly deemed this provider "Recommended", per Ribbon's cost/quality
    # and ratings metrics, as surfaced in the Provider Search.
    recommendation_status = models.TextField(
        max_length=255,
        null=True,
        blank=True,
        choices=RecommendationStatusChoices.choices,
    )
    # Stores the composite score of the provider, which is calculated based on cost, quality and rating
    composite_score = models.FloatField(null=True, blank=True, default=None)
    # Prospect name
    target_name = models.TextField(
        max_length=255,
        blank=False,
        null=False,
    )
    # Stores whether a row has been already processed for the recommendation analysis
    is_processed = models.BooleanField(null=True, blank=True, default=False)
    # Unique job name for a target provider list
    import_job_name = models.TextField(
        max_length=255,
        blank=False,
        null=False,
    )
    # Stores the raw search results received from the vendor
    results = JSONField(null=True, blank=True)
    # Stores the name of vendor for which the search request was made
    vendor = models.TextField(
        max_length=100,
        null=True,
        blank=True,
        choices=ProviderRecommendationAnalysisVendor.choices,
    )
    # Stores whether provider is in network
    in_network = models.BooleanField(
        null=True,
        blank=True,
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["npi", "import_job_name"],
                condition=Q(deleted=None),
                name="unique_npi_import_job_name_if_not_deleted",
            )
        ]
